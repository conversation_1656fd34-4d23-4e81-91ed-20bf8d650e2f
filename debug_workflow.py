"""Debug script to test the OpenRouter workflow without full dependencies."""

import logging
import sys
import os

# Configure logging to see all debug information
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_openrouter_api_call():
    """Test OpenRouter API call with a simple example."""
    
    # Test data
    test_content = """
    This is a comprehensive guide to Python programming. 
    Python is a high-level, interpreted programming language with dynamic semantics. 
    Its high-level built in data structures, combined with dynamic typing and dynamic binding, 
    make it very attractive for Rapid Application Development, as well as for use as a 
    scripting or glue language to connect existing components together.
    """
    
    test_title = "Python Programming Guide"
    test_url = "https://example.com/python-guide"
    test_model = "deepseek/deepseek-r1-0528:free"
    
    # You would need to provide a real API key here
    test_api_key = "sk-or-v1-your-real-api-key-here"  # Replace with real key
    
    logger.info("=" * 60)
    logger.info("TESTING OPENROUTER API WORKFLOW")
    logger.info("=" * 60)
    
    logger.info(f"Test content length: {len(test_content)} characters")
    logger.info(f"Test title: {test_title}")
    logger.info(f"Test URL: {test_url}")
    logger.info(f"Test model: {test_model}")
    logger.info(f"API key provided: {test_api_key != 'sk-or-v1-your-real-api-key-here'}")
    
    if test_api_key == "sk-or-v1-your-real-api-key-here":
        logger.warning("⚠️  No real API key provided - this is just a simulation")
        logger.info("To test with real API:")
        logger.info("1. Get API key from https://openrouter.ai/keys")
        logger.info("2. Replace test_api_key in this script")
        logger.info("3. Run the script again")
        return False
    
    try:
        # Import the OpenRouter client
        from openrouter_client import generate_description_sync
        
        logger.info("📞 Calling OpenRouter API...")
        
        result = generate_description_sync(
            content=test_content,
            title=test_title,
            url=test_url,
            model=test_model,
            api_key=test_api_key
        )
        
        if result:
            logger.info("✅ API call successful!")
            logger.info(f"Generated description: {result}")
            logger.info(f"Description length: {len(result)} characters")
            return True
        else:
            logger.error("❌ API call failed - no result returned")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("Make sure all dependencies are installed:")
        logger.info("pip install httpx python-dotenv")
        return False
    except Exception as e:
        logger.error(f"❌ Error during API call: {e}")
        return False

def test_content_extraction():
    """Test content extraction workflow."""
    
    logger.info("=" * 60)
    logger.info("TESTING CONTENT EXTRACTION WORKFLOW")
    logger.info("=" * 60)
    
    test_url = "https://httpbin.org/html"  # Simple test page
    
    try:
        # Test basic requests-based extraction
        import requests
        from bs4 import BeautifulSoup
        
        logger.info(f"📥 Fetching content from: {test_url}")
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        response = requests.get(test_url, headers=headers, timeout=10)
        response.raise_for_status()
        
        logger.info(f"✅ Successfully fetched {len(response.text)} characters")
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract title
        title = soup.title.string if soup.title else "No title"
        logger.info(f"📄 Title: {title}")
        
        # Extract text content
        text_content = soup.get_text(separator=' ', strip=True)
        logger.info(f"📝 Text content length: {len(text_content)} characters")
        logger.info(f"📝 Content preview: {text_content[:200]}...")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("Make sure dependencies are installed:")
        logger.info("pip install requests beautifulsoup4")
        return False
    except Exception as e:
        logger.error(f"❌ Error during content extraction: {e}")
        return False

def test_workflow_integration():
    """Test the complete workflow integration."""
    
    logger.info("=" * 60)
    logger.info("TESTING COMPLETE WORKFLOW INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Test the enhanced content extraction function
        from app import get_page_content_enhanced
        
        test_url = "https://httpbin.org/html"
        test_api_key = "sk-or-v1-your-real-api-key-here"  # Replace with real key
        
        logger.info(f"🔄 Testing enhanced content extraction for: {test_url}")
        
        # Test without LLM
        logger.info("Testing without LLM...")
        title1, desc1, content1 = get_page_content_enhanced(
            url=test_url,
            use_puppeteer=False,
            use_llm=False
        )
        
        logger.info(f"✅ Without LLM - Title: {title1}")
        logger.info(f"✅ Without LLM - Description: {desc1}")
        logger.info(f"✅ Without LLM - Content length: {len(content1)}")
        
        # Test with LLM (if API key provided)
        if test_api_key != "sk-or-v1-your-real-api-key-here":
            logger.info("Testing with LLM...")
            title2, desc2, content2 = get_page_content_enhanced(
                url=test_url,
                use_puppeteer=False,
                use_llm=True,
                llm_model="deepseek/deepseek-r1-0528:free",
                api_key=test_api_key
            )
            
            logger.info(f"✅ With LLM - Title: {title2}")
            logger.info(f"✅ With LLM - Description: {desc2}")
            logger.info(f"✅ With LLM - Content length: {len(content2)}")
        else:
            logger.info("⚠️  Skipping LLM test - no API key provided")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error during workflow test: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all debug tests."""
    
    logger.info("🧪 LLMS.txt Generator - Workflow Debug Tool")
    logger.info("=" * 60)
    
    results = []
    
    # Test 1: Content Extraction
    logger.info("\n🔍 TEST 1: Content Extraction")
    results.append(test_content_extraction())
    
    # Test 2: OpenRouter API
    logger.info("\n🤖 TEST 2: OpenRouter API")
    results.append(test_openrouter_api_call())
    
    # Test 3: Workflow Integration
    logger.info("\n🔄 TEST 3: Workflow Integration")
    results.append(test_workflow_integration())
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    test_names = ["Content Extraction", "OpenRouter API", "Workflow Integration"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The workflow should be working correctly.")
    else:
        logger.info("⚠️  Some tests failed. Check the logs above for details.")
        logger.info("\n💡 Common issues:")
        logger.info("- Missing dependencies (install with: pip install -r requirements.txt)")
        logger.info("- No OpenRouter API key provided")
        logger.info("- Network connectivity issues")

if __name__ == "__main__":
    main()
