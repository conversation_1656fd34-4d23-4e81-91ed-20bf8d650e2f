"""Test the improved title and description generation."""

import sys
import os

# Add current directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_title_generation():
    """Test intelligent title generation from URLs."""
    
    print("🧪 Testing Title Generation")
    print("=" * 40)
    
    try:
        from content_extractor import ContentExtractor
        
        extractor = ContentExtractor()
        
        test_cases = [
            # (URL, Expected Pattern)
            ("https://docs.example.com/api/authentication", "Authentication"),
            ("https://docs.example.com/guides/getting-started", "Getting Started Guide"),
            ("https://docs.example.com/examples/basic-usage", "Basic Usage Example"),
            ("https://docs.example.com/dashboard/settings", "Settings - Dashboard"),
            ("https://docs.example.com/integrations/react", "React - Integrations"),
            ("https://docs.example.com/concepts/overview", "Concepts Overview"),
            ("https://docs.example.com/tutorials/advanced-features", "Advanced Features Guide"),
            ("https://docs.example.com/faq", "Faq"),
            ("https://example.com/", "Example"),
            ("https://api.service.com/v1/users", "Users - Api")
        ]
        
        print("📝 Title Generation Results:")
        all_passed = True
        
        for url, expected_pattern in test_cases:
            generated_title = extractor._generate_title_from_url(url)
            
            # Check if the generated title contains expected elements
            contains_expected = any(word.lower() in generated_title.lower() for word in expected_pattern.split())
            
            status = "✅" if contains_expected else "❌"
            print(f"  {status} {url}")
            print(f"      Generated: '{generated_title}'")
            print(f"      Expected pattern: '{expected_pattern}'")
            
            if not contains_expected:
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during title generation test: {e}")
        return False

def test_description_generation():
    """Test intelligent description generation from URLs."""
    
    print("\n🧪 Testing Description Generation")
    print("=" * 40)
    
    try:
        from content_extractor import ContentExtractor
        
        extractor = ContentExtractor()
        
        test_cases = [
            # (URL, Title, Expected Keywords)
            ("https://docs.example.com/api/authentication", "Authentication", ["API", "reference", "documentation"]),
            ("https://docs.example.com/guides/getting-started", "Getting Started", ["guide", "covering"]),
            ("https://docs.example.com/examples/basic-usage", "Basic Usage", ["example", "demonstrating"]),
            ("https://docs.example.com/dashboard/settings", "Settings", ["dashboard", "documentation"]),
            ("https://docs.example.com/integrations/react", "React Integration", ["integration", "guide"]),
            ("https://docs.example.com/concepts/overview", "Overview", ["overview", "concepts"]),
            ("https://docs.example.com/faq", "FAQ", ["help", "support"]),
            ("https://docs.example.com/tutorials/advanced", "Advanced Tutorial", ["guide", "covering"]),
            ("https://docs.example.com/help/support", "Support", ["help", "support"]),
            ("https://docs.example.com/general/info", "General Info", ["documentation", "covering"])
        ]
        
        print("📝 Description Generation Results:")
        all_passed = True
        
        for url, title, expected_keywords in test_cases:
            generated_desc = extractor._generate_description_from_url(url, title)
            
            # Check if the generated description contains expected keywords
            contains_keywords = any(keyword.lower() in generated_desc.lower() for keyword in expected_keywords)
            
            status = "✅" if contains_keywords else "❌"
            print(f"  {status} {url}")
            print(f"      Title: '{title}'")
            print(f"      Generated: '{generated_desc}'")
            print(f"      Expected keywords: {expected_keywords}")
            
            if not contains_keywords:
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during description generation test: {e}")
        return False

def test_content_extraction_fallbacks():
    """Test the complete content extraction with fallbacks."""
    
    print("\n🧪 Testing Content Extraction Fallbacks")
    print("=" * 40)
    
    try:
        from content_extractor import extract_content_sync
        
        # Test with non-existent URLs to trigger fallback logic
        test_urls = [
            "https://docs.example.com/api/authentication",
            "https://docs.example.com/guides/getting-started", 
            "https://docs.example.com/examples/basic-usage",
            "https://docs.example.com/dashboard/settings"
        ]
        
        print("📝 Content Extraction Results:")
        all_passed = True
        
        for url in test_urls:
            title, description, content = extract_content_sync(url, use_puppeteer=False)
            
            # Check that we don't get generic "Page at" titles
            has_good_title = not title.startswith("Page at ")
            has_good_desc = description != "Resource information"
            
            status = "✅" if has_good_title and has_good_desc else "❌"
            print(f"  {status} {url}")
            print(f"      Title: '{title}'")
            print(f"      Description: '{description}'")
            print(f"      Content length: {len(content)}")
            
            if not (has_good_title and has_good_desc):
                all_passed = False
                if not has_good_title:
                    print(f"      ❌ Generic title detected")
                if not has_good_desc:
                    print(f"      ❌ Generic description detected")
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during content extraction test: {e}")
        return False

def test_llms_txt_format():
    """Test that the final llms.txt format is correct."""
    
    print("\n🧪 Testing LLMS.txt Format")
    print("=" * 40)
    
    try:
        from app import generate_llms_txt
        
        # Test URLs
        test_urls = [
            "https://docs.example.com/introduction",
            "https://docs.example.com/api/authentication",
            "https://docs.example.com/guides/getting-started"
        ]
        
        site_name = "Example Documentation"
        site_description = "Comprehensive documentation for Example API"
        
        print(f"📝 Generating llms.txt for testing...")
        
        # Generate without LLM to test basic functionality
        llms_content = generate_llms_txt(
            urls=test_urls,
            site_name=site_name,
            site_description=site_description,
            use_puppeteer=False,
            use_llm=False
        )
        
        print(f"\n📋 Generated Content Preview:")
        print("=" * 30)
        lines = llms_content.split('\n')
        for i, line in enumerate(lines[:20]):  # Show first 20 lines
            print(f"{i+1:2d}: {line}")
        if len(lines) > 20:
            print(f"... and {len(lines)-20} more lines")
        print("=" * 30)
        
        # Check format requirements
        checks = []
        
        # Check for proper title format
        link_lines = [line for line in lines if line.startswith('- [')]
        checks.append(("Has link entries", len(link_lines) > 0))
        
        # Check that no entries have "Page at" titles
        bad_titles = [line for line in link_lines if "Page at " in line]
        checks.append(("No 'Page at' titles", len(bad_titles) == 0))
        
        # Check that no entries have "Resource information" descriptions
        bad_descriptions = [line for line in link_lines if "Resource information" in line]
        checks.append(("No generic descriptions", len(bad_descriptions) == 0))
        
        # Check for proper structure
        has_main_header = any(line.startswith(f"# {site_name}") for line in lines)
        checks.append(("Has main header", has_main_header))
        
        has_sections = any(line.startswith("## ") for line in lines)
        checks.append(("Has sections", has_sections))
        
        print(f"\n📊 Format Validation:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if bad_titles:
            print(f"\n❌ Found bad titles:")
            for line in bad_titles:
                print(f"    {line}")
        
        if bad_descriptions:
            print(f"\n❌ Found generic descriptions:")
            for line in bad_descriptions:
                print(f"    {line}")
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during format test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests."""
    
    print("🧪 Title & Description Improvements Test Suite")
    print("=" * 60)
    
    tests = [
        ("Title Generation", test_title_generation),
        ("Description Generation", test_description_generation),
        ("Content Extraction Fallbacks", test_content_extraction_fallbacks),
        ("LLMS.txt Format", test_llms_txt_format)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            result = test_func()
            results.append(result)
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"\n{status} {test_name}")
        except Exception as e:
            print(f"\n❌ FAIL {test_name}: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    for i, (test_name, result) in enumerate(zip([t[0] for t in tests], results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Title and description generation is working correctly.")
        print("\n💡 Key improvements:")
        print("   ✓ No more 'Page at [URL]' generic titles")
        print("   ✓ No more 'Resource information' generic descriptions")
        print("   ✓ Intelligent fallbacks based on URL structure")
        print("   ✓ Contextual descriptions that explain page purpose")
    else:
        print("⚠️ Some tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
