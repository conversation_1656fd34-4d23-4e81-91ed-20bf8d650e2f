"""Test the new categorization and llms.txt structure."""

import sys
import os

# Add current directory to path to import app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_categorization():
    """Test URL categorization with sample URLs."""
    
    print("🧪 Testing URL Categorization")
    print("=" * 50)
    
    # Sample URLs that would typically be found in documentation sites
    test_urls = [
        "https://example.com/docs/index",
        "https://example.com/docs/introduction",
        "https://example.com/docs/getting-started",
        "https://example.com/docs/quickstart",
        "https://example.com/docs/api/authentication",
        "https://example.com/docs/api/endpoints",
        "https://example.com/docs/api/reference",
        "https://example.com/docs/examples/basic",
        "https://example.com/docs/examples/advanced",
        "https://example.com/docs/guides/installation",
        "https://example.com/docs/guides/best-practices",
        "https://example.com/docs/dashboard/overview",
        "https://example.com/docs/dashboard/settings",
        "https://example.com/docs/integrations/react",
        "https://example.com/docs/integrations/vue",
        "https://example.com/docs/concepts/architecture",
        "https://example.com/docs/features/security",
        "https://example.com/docs/help/faq",
        "https://example.com/docs/support/contact"
    ]
    
    try:
        from app import categorize_urls
        
        print(f"📥 Input: {len(test_urls)} test URLs")
        print("\nSample URLs:")
        for i, url in enumerate(test_urls[:5]):
            print(f"  {i+1}. {url}")
        print(f"  ... and {len(test_urls)-5} more")
        
        # Test categorization
        categorized = categorize_urls(test_urls)
        
        print(f"\n📊 Categorization Results:")
        print(f"Found {len(categorized)} categories")
        
        total_categorized = 0
        for category, urls in categorized.items():
            print(f"\n📁 {category} ({len(urls)} URLs):")
            total_categorized += len(urls)
            for url in urls:
                # Extract the path part for display
                path = url.split('/')[-1] or url.split('/')[-2]
                print(f"  - {path}")
        
        print(f"\n✅ Total URLs categorized: {total_categorized}/{len(test_urls)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during categorization: {e}")
        return False

def test_llms_txt_structure():
    """Test the llms.txt generation structure."""
    
    print("\n🧪 Testing LLMS.txt Structure")
    print("=" * 50)
    
    # Sample data
    test_urls = [
        "https://docs.example.com/introduction",
        "https://docs.example.com/getting-started",
        "https://docs.example.com/api/authentication",
        "https://docs.example.com/examples/basic",
        "https://docs.example.com/guides/installation"
    ]
    
    site_name = "Example Documentation"
    site_description = "Comprehensive documentation for Example API and services."
    
    try:
        from app import generate_llms_txt
        
        print(f"📝 Generating llms.txt for: {site_name}")
        print(f"📄 Description: {site_description}")
        print(f"🔗 URLs: {len(test_urls)}")
        
        # Generate without enhanced features for testing
        llms_content = generate_llms_txt(
            urls=test_urls,
            site_name=site_name,
            site_description=site_description,
            use_puppeteer=False,
            use_llm=False
        )
        
        print(f"\n📋 Generated LLMS.txt Content:")
        print("=" * 30)
        print(llms_content)
        print("=" * 30)
        
        # Analyze structure
        lines = llms_content.split('\n')
        headers = [line for line in lines if line.startswith('#')]
        sections = [line for line in lines if line.startswith('##')]
        links = [line for line in lines if line.startswith('- [')]
        
        print(f"\n📊 Structure Analysis:")
        print(f"  📌 Main header: {len([h for h in headers if h.startswith('# ')])}")
        print(f"  📂 Sections: {len(sections)}")
        print(f"  🔗 Links: {len(links)}")
        
        print(f"\n📂 Sections found:")
        for section in sections:
            print(f"  - {section}")
        
        # Check if it follows the expected structure
        expected_elements = [
            f"# {site_name}",
            "## Introduction",
            site_description
        ]
        
        structure_valid = True
        for element in expected_elements:
            if element not in llms_content:
                print(f"❌ Missing expected element: {element}")
                structure_valid = False
        
        if structure_valid:
            print(f"\n✅ Structure validation passed!")
        else:
            print(f"\n⚠️ Structure validation had issues")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during generation: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def show_cosmic_comparison():
    """Show how our structure compares to the Cosmic example."""
    
    print("\n🌟 Cosmic Documentation Structure Comparison")
    print("=" * 60)
    
    print("📖 Cosmic Example Structure:")
    cosmic_structure = [
        "# Cosmic Documentation",
        "## Introduction",
        "## Get started", 
        "## Dashboard",
        "## API reference",
        "## Other resources"
    ]
    
    for item in cosmic_structure:
        print(f"  {item}")
    
    print(f"\n🚀 Our Generator Structure:")
    our_structure = [
        "# [Site Name]",
        "## Introduction", 
        "## Get started",
        "## Documentation", 
        "## API reference",
        "## Examples",
        "## Guides",
        "## Dashboard",
        "## Integrations",
        "## Other resources"
    ]
    
    for item in our_structure:
        print(f"  {item}")
    
    print(f"\n✅ Key Improvements:")
    improvements = [
        "✓ Automatic categorization based on URL patterns",
        "✓ More granular categories (Examples, Guides, Integrations)",
        "✓ AI-generated descriptions for better context",
        "✓ Flexible structure that adapts to content",
        "✓ Enhanced processing with JavaScript rendering"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")

def main():
    """Run all tests."""
    
    print("🧪 LLMS.txt Structure & Categorization Test")
    print("=" * 60)
    
    results = []
    
    # Test 1: Categorization
    results.append(test_categorization())
    
    # Test 2: LLMS.txt Structure
    results.append(test_llms_txt_structure())
    
    # Show comparison
    show_cosmic_comparison()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    test_names = ["URL Categorization", "LLMS.txt Structure"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The new structure is working correctly.")
        print("\n💡 The generator now creates properly structured llms.txt files")
        print("   that follow the same organizational principles as the Cosmic example.")
    else:
        print("⚠️ Some tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
