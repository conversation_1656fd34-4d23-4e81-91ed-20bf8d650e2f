"""OpenRouter API client for LLM integration."""

import httpx
import json
import logging
from typing import Optional, Dict, Any
from config import OP<PERSON>ROUTER_API_KEY, OPENROUTER_BASE_URL, DEFAULT_MODEL

logger = logging.getLogger(__name__)

class OpenRouterClient:
    """Client for interacting with OpenRouter API."""
    
    def __init__(self, api_key: str = None):
        """Initialize the OpenRouter client.
        
        Args:
            api_key: OpenRouter API key. If not provided, uses config default.
        """
        self.api_key = api_key or OPENROUTER_API_KEY
        self.base_url = OPENROUTER_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo/llms-txt-generator",
            "X-Title": "LLMS.txt Generator"
        }
    
    async def generate_description(
        self,
        content: str,
        title: str = "",
        url: str = "",
        model: str = DEFAULT_MODEL,
        max_tokens: int = 200
    ) -> Optional[str]:
        """Generate a description for web page content using LLM.

        Args:
            content: The main content of the web page
            title: The page title (optional)
            url: The page URL (optional)
            model: The LLM model to use
            max_tokens: Maximum tokens in response

        Returns:
            Generated description or None if failed
        """
        # Enhanced validation and logging
        if not self.api_key or not self.api_key.strip():
            logger.error("No OpenRouter API key provided or API key is empty")
            return None

        if not content or not content.strip():
            logger.error("Empty or None content provided for description generation")
            return None

        # Log content length for debugging
        content_length = len(content.strip())
        logger.info(f"Processing content of length {content_length} characters for URL: {url}")

        # Create the prompt
        prompt = self._create_description_prompt(content, title, url)
        prompt_length = len(prompt)
        logger.info(f"Generated prompt of length {prompt_length} characters")

        # Prepare request payload
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": max_tokens,
            "temperature": 0.3,
            "top_p": 0.9
        }

        # Log API call details (without exposing API key)
        logger.info(f"Making OpenRouter API call to {self.base_url}/chat/completions")
        logger.info(f"Using model: {model}")
        logger.info(f"API key present: {bool(self.api_key and len(self.api_key) > 10)}")
        logger.info(f"API key starts with: {self.api_key[:10] if self.api_key else 'None'}...")

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )

                logger.info(f"OpenRouter API response status: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"API response received: {type(result)}")

                    if "choices" in result and len(result["choices"]) > 0:
                        description = result["choices"][0]["message"]["content"].strip()
                        logger.info(f"Successfully generated description of length {len(description)}")
                        return description
                    else:
                        logger.error(f"Unexpected response format - missing choices: {result}")
                        return None
                else:
                    error_text = response.text
                    logger.error(f"OpenRouter API error: {response.status_code}")
                    logger.error(f"Error response: {error_text}")

                    # Try to parse error details
                    try:
                        error_json = response.json()
                        if "error" in error_json:
                            logger.error(f"API error details: {error_json['error']}")
                    except:
                        pass

                    return None

        except httpx.TimeoutException as e:
            logger.error(f"OpenRouter API timeout: {str(e)}")
            return None
        except httpx.RequestError as e:
            logger.error(f"OpenRouter API request error: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calling OpenRouter API: {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            return None
    
    def _create_description_prompt(self, content: str, title: str = "", url: str = "") -> str:
        """Create a prompt for generating page descriptions.
        
        Args:
            content: The main content of the web page
            title: The page title (optional)
            url: The page URL (optional)
            
        Returns:
            Formatted prompt string
        """
        prompt_parts = [
            "Please analyze the following web page content and generate a concise, informative description (1-2 sentences) that summarizes what this page is about.",
            "Focus on the main topic, purpose, and key information covered.",
            "Make the description useful for someone deciding whether to visit this page."
        ]
        
        if title:
            prompt_parts.append(f"\nPage Title: {title}")
        
        if url:
            prompt_parts.append(f"Page URL: {url}")
        
        prompt_parts.extend([
            f"\nPage Content:\n{content[:6000]}",  # Limit content to avoid token limits
            "\nGenerate a description:"
        ])
        
        return "\n".join(prompt_parts)
    
    def is_configured(self) -> bool:
        """Check if the client is properly configured with an API key.
        
        Returns:
            True if API key is available, False otherwise
        """
        return bool(self.api_key and self.api_key.strip())


# Synchronous wrapper for easier integration
def generate_description_sync(
    content: str,
    title: str = "",
    url: str = "",
    model: str = DEFAULT_MODEL,
    api_key: str = None
) -> Optional[str]:
    """Synchronous wrapper for generating descriptions.

    Args:
        content: The main content of the web page
        title: The page title (optional)
        url: The page URL (optional)
        model: The LLM model to use
        api_key: OpenRouter API key (optional)

    Returns:
        Generated description or None if failed
    """
    import asyncio

    # Enhanced validation
    if not api_key or not api_key.strip():
        logger.error("generate_description_sync: No API key provided")
        return None

    if not content or not content.strip():
        logger.error("generate_description_sync: No content provided")
        return None

    logger.info(f"generate_description_sync called for URL: {url}")
    logger.info(f"Content length: {len(content)} characters")
    logger.info(f"Model: {model}")
    logger.info(f"API key provided: {bool(api_key and len(api_key) > 10)}")

    client = OpenRouterClient(api_key)

    try:
        # Check if there's already an event loop running
        try:
            loop = asyncio.get_running_loop()
            logger.info("Found existing event loop, creating new one")
            # If there's already a loop, create a new one in a thread
            import concurrent.futures
            import threading

            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    result = new_loop.run_until_complete(
                        client.generate_description(content, title, url, model)
                    )
                    return result
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                result = future.result(timeout=60)  # 60 second timeout

        except RuntimeError:
            # No event loop running, create a new one
            logger.info("No existing event loop, creating new one")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    client.generate_description(content, title, url, model)
                )
            finally:
                loop.close()

        logger.info(f"generate_description_sync result: {result is not None}")
        if result:
            logger.info(f"Generated description length: {len(result)}")

        return result

    except Exception as e:
        logger.error(f"Error in synchronous description generation: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None
