"""Simple test to verify OpenRouter API integration without external dependencies."""

import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_api_request_format():
    """Test that we're formatting the API request correctly."""
    
    logger.info("Testing OpenRouter API request format...")
    
    # Test data
    test_content = "This is a test article about Python programming. Python is a versatile language."
    test_title = "Python Guide"
    test_url = "https://example.com/python"
    test_model = "deepseek/deepseek-r1-0528:free"
    test_api_key = "sk-or-v1-test-key"
    
    # Simulate the request payload that would be sent
    payload = {
        "model": test_model,
        "messages": [
            {
                "role": "user",
                "content": f"""Please analyze the following web page content and generate a concise, informative description (1-2 sentences) that summarizes what this page is about.
Focus on the main topic, purpose, and key information covered.
Make the description useful for someone deciding whether to visit this page.

Page Title: {test_title}
Page URL: {test_url}

Page Content:
{test_content}

Generate a description:"""
            }
        ],
        "max_tokens": 200,
        "temperature": 0.3,
        "top_p": 0.9
    }
    
    # Test headers
    headers = {
        "Authorization": f"Bearer {test_api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/your-repo/llms-txt-generator",
        "X-Title": "LLMS.txt Generator"
    }
    
    logger.info("✅ Request payload structure:")
    logger.info(f"  Model: {payload['model']}")
    logger.info(f"  Messages: {len(payload['messages'])} message(s)")
    logger.info(f"  Max tokens: {payload['max_tokens']}")
    logger.info(f"  Temperature: {payload['temperature']}")
    
    logger.info("✅ Headers structure:")
    logger.info(f"  Authorization: Bearer {test_api_key[:10]}...")
    logger.info(f"  Content-Type: {headers['Content-Type']}")
    logger.info(f"  HTTP-Referer: {headers['HTTP-Referer']}")
    
    logger.info("✅ Prompt structure:")
    prompt = payload['messages'][0]['content']
    logger.info(f"  Prompt length: {len(prompt)} characters")
    logger.info(f"  Contains title: {'Page Title:' in prompt}")
    logger.info(f"  Contains URL: {'Page URL:' in prompt}")
    logger.info(f"  Contains content: {'Page Content:' in prompt}")
    
    # Validate JSON serialization
    try:
        json_payload = json.dumps(payload)
        logger.info(f"✅ JSON serialization successful: {len(json_payload)} bytes")
    except Exception as e:
        logger.error(f"❌ JSON serialization failed: {e}")
        return False
    
    return True

def test_model_validation():
    """Test model name validation."""
    
    logger.info("Testing model validation...")
    
    # Import validation function
    try:
        from config import validate_custom_model
        
        test_models = [
            ("deepseek/deepseek-r1-0528:free", True),
            ("openai/gpt-4.1", True),
            ("anthropic/claude-3.5-sonnet", True),
            ("invalid-model", False),
            ("", False),
            ("provider/", False),
            ("/model", False)
        ]
        
        all_passed = True
        for model, expected in test_models:
            result = validate_custom_model(model)
            status = "✅" if result == expected else "❌"
            logger.info(f"  {status} {model}: {result} (expected {expected})")
            if result != expected:
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        logger.error(f"❌ Could not import validation function: {e}")
        return False

def test_content_length_validation():
    """Test content length validation logic."""
    
    logger.info("Testing content length validation...")
    
    try:
        from config import MIN_CONTENT_LENGTH
        
        test_cases = [
            ("Short", False),
            ("A" * (MIN_CONTENT_LENGTH - 1), False),
            ("A" * MIN_CONTENT_LENGTH, True),
            ("A" * (MIN_CONTENT_LENGTH + 100), True)
        ]
        
        all_passed = True
        for content, should_process in test_cases:
            meets_requirement = len(content) >= MIN_CONTENT_LENGTH
            status = "✅" if meets_requirement == should_process else "❌"
            logger.info(f"  {status} Content length {len(content)}: {meets_requirement} (expected {should_process})")
            if meets_requirement != should_process:
                all_passed = False
        
        logger.info(f"✅ Minimum content length: {MIN_CONTENT_LENGTH} characters")
        return all_passed
        
    except ImportError as e:
        logger.error(f"❌ Could not import MIN_CONTENT_LENGTH: {e}")
        return False

def test_api_key_validation():
    """Test API key validation logic."""
    
    logger.info("Testing API key validation...")
    
    test_cases = [
        ("", False),
        (None, False),
        ("   ", False),
        ("sk-or-v1-", False),
        ("sk-or-v1-valid-key-here", True),
        ("sk-or-v1-" + "x" * 20, True)
    ]
    
    all_passed = True
    for api_key, should_be_valid in test_cases:
        is_valid = bool(api_key and api_key.strip() and len(api_key) > 10)
        status = "✅" if is_valid == should_be_valid else "❌"
        key_display = repr(api_key) if api_key else "None"
        logger.info(f"  {status} {key_display}: {is_valid} (expected {should_be_valid})")
        if is_valid != should_be_valid:
            all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    
    logger.info("🧪 OpenRouter API Integration Test")
    logger.info("=" * 50)
    
    tests = [
        ("API Request Format", test_api_request_format),
        ("Model Validation", test_model_validation),
        ("Content Length Validation", test_content_length_validation),
        ("API Key Validation", test_api_key_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 {test_name}")
        logger.info("-" * 30)
        try:
            result = test_func()
            results.append(result)
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            logger.error(f"❌ FAIL {test_name}: {e}")
            results.append(False)
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, result) in enumerate(zip([t[0] for t in tests], results)):
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{i+1}. {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All validation tests passed!")
        logger.info("The API integration should work correctly with a valid API key.")
    else:
        logger.info("⚠️  Some tests failed. Check the implementation.")
    
    logger.info("\n💡 Next steps:")
    logger.info("1. Install dependencies: pip install httpx beautifulsoup4 readability-lxml pyppeteer")
    logger.info("2. Get OpenRouter API key: https://openrouter.ai/keys")
    logger.info("3. Test with real API key using debug_workflow.py")

if __name__ == "__main__":
    main()
