"""Enhanced content extraction with <PERSON><PERSON>peteer and main content filtering."""

import asyncio
import logging
import re
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
from bs4 import BeautifulSoup
from readability import Document
import requests
from pyppeteer import launch
from config import (
    PUPPETEER_TIMEOUT, 
    PUPPETEER_WAIT_UNTIL, 
    USER_AGENT, 
    REQUEST_TIMEOUT,
    MAX_CONTENT_LENGTH,
    MIN_CONTENT_LENGTH
)

logger = logging.getLogger(__name__)

class ContentExtractor:
    """Enhanced content extractor with Puppeteer support and main content filtering."""
    
    def __init__(self, use_puppeteer: bool = False):
        """Initialize the content extractor.
        
        Args:
            use_puppeteer: Whether to use P<PERSON>peteer for JavaScript rendering
        """
        self.use_puppeteer = use_puppeteer
        self.browser = None
    
    async def get_page_content_puppeteer(self, url: str) -> str:
        """Fetch page content using Puppeteer for JavaScript rendering.
        
        Args:
            url: The URL to fetch
            
        Returns:
            HTML content as string
        """
        try:
            if not self.browser:
                self.browser = await launch(
                    headless=True,
                    args=['--no-sandbox', '--disable-setuid-sandbox']
                )
            
            page = await self.browser.newPage()
            await page.setUserAgent(USER_AGENT)
            
            # Set viewport
            await page.setViewport({'width': 1920, 'height': 1080})
            
            # Navigate to page and wait for content
            await page.goto(url, {
                'waitUntil': PUPPETEER_WAIT_UNTIL,
                'timeout': PUPPETEER_TIMEOUT
            })
            
            # Get the HTML content
            content = await page.content()
            await page.close()
            
            return content
            
        except Exception as e:
            logger.error(f"Error fetching content with Puppeteer for {url}: {str(e)}")
            return ""
    
    def get_page_content_requests(self, url: str) -> str:
        """Fetch page content using requests (traditional method).

        Args:
            url: The URL to fetch

        Returns:
            HTML content as string
        """
        try:
            logger.info(f"Fetching content using requests for: {url}")
            headers = {"User-Agent": USER_AGENT}
            response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            content_length = len(response.text)
            logger.info(f"Successfully fetched {content_length} characters from {url}")
            return response.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching content with requests for {url}: {str(e)}")
            return ""
    
    async def get_page_content(self, url: str) -> str:
        """Get page content using the configured method.
        
        Args:
            url: The URL to fetch
            
        Returns:
            HTML content as string
        """
        if self.use_puppeteer:
            return await self.get_page_content_puppeteer(url)
        else:
            return self.get_page_content_requests(url)
    
    def extract_main_content(self, html_content: str) -> str:
        """Extract main content from HTML, excluding headers, footers, and navigation.

        Args:
            html_content: Raw HTML content

        Returns:
            Cleaned main content as text
        """
        if not html_content:
            logger.warning("No HTML content provided for main content extraction")
            return ""

        logger.info(f"Extracting main content from HTML of length {len(html_content)}")

        try:
            # Use readability to extract main content
            doc = Document(html_content)
            main_content_html = doc.summary()
            logger.info(f"Readability extracted content of length {len(main_content_html)}")

            # Parse with BeautifulSoup for further cleaning
            soup = BeautifulSoup(main_content_html, 'html.parser')

            # Remove unwanted elements
            unwanted_tags = [
                'script', 'style', 'nav', 'header', 'footer',
                'aside', 'advertisement', 'ads', 'sidebar',
                'menu', 'breadcrumb', 'social', 'share'
            ]

            removed_count = 0
            for tag in unwanted_tags:
                elements = soup.find_all(tag)
                removed_count += len(elements)
                for element in elements:
                    element.decompose()

            logger.info(f"Removed {removed_count} unwanted tag elements")

            # Remove elements with unwanted classes/ids
            unwanted_patterns = [
                'nav', 'header', 'footer', 'sidebar', 'menu',
                'advertisement', 'ads', 'social', 'share',
                'breadcrumb', 'pagination', 'related', 'comment'
            ]

            pattern_removed_count = 0
            for pattern in unwanted_patterns:
                # Remove by class
                class_elements = soup.find_all(class_=re.compile(pattern, re.I))
                pattern_removed_count += len(class_elements)
                for element in class_elements:
                    element.decompose()
                # Remove by id
                id_elements = soup.find_all(id=re.compile(pattern, re.I))
                pattern_removed_count += len(id_elements)
                for element in id_elements:
                    element.decompose()

            logger.info(f"Removed {pattern_removed_count} elements by pattern matching")

            # Extract text content
            text_content = soup.get_text(separator=' ', strip=True)

            # Clean up whitespace
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            original_length = len(text_content)

            # Limit content length
            if len(text_content) > MAX_CONTENT_LENGTH:
                text_content = text_content[:MAX_CONTENT_LENGTH] + "..."
                logger.info(f"Truncated content from {original_length} to {MAX_CONTENT_LENGTH} characters")

            logger.info(f"Final extracted content length: {len(text_content)} characters")

            return text_content

        except Exception as e:
            logger.error(f"Error extracting main content: {str(e)}")
            # Fallback to basic text extraction
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                fallback_content = soup.get_text(separator=' ', strip=True)[:MAX_CONTENT_LENGTH]
                logger.info(f"Using fallback extraction, got {len(fallback_content)} characters")
                return fallback_content
            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed: {str(fallback_error)}")
                return ""
    
    def extract_title_and_meta(self, html_content: str, url: str = "") -> Tuple[str, str]:
        """Extract title and meta description from HTML with intelligent fallbacks.

        Args:
            html_content: Raw HTML content
            url: The page URL (for fallback title)

        Returns:
            Tuple of (title, meta_description)
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Get title with multiple fallback strategies
            title = ""

            # 1. Try HTML title tag
            if soup.title and soup.title.string:
                title = soup.title.string.strip()
                logger.info(f"Extracted title from <title> tag: {title}")

            # 2. Try Open Graph title
            if not title:
                og_title = soup.find("meta", attrs={"property": "og:title"})
                if og_title and og_title.get("content"):
                    title = og_title.get("content").strip()
                    logger.info(f"Extracted title from og:title: {title}")

            # 3. Try Twitter title
            if not title:
                twitter_title = soup.find("meta", attrs={"name": "twitter:title"})
                if twitter_title and twitter_title.get("content"):
                    title = twitter_title.get("content").strip()
                    logger.info(f"Extracted title from twitter:title: {title}")

            # 4. Try first h1 tag
            if not title:
                h1 = soup.find("h1")
                if h1 and h1.get_text():
                    title = h1.get_text().strip()
                    logger.info(f"Extracted title from <h1>: {title}")

            # 5. Intelligent URL-based fallback
            if not title and url:
                title = self._generate_title_from_url(url)
                logger.info(f"Generated title from URL: {title}")

            # Get meta description with multiple fallback strategies
            description = ""

            # 1. Try meta description
            meta_desc = soup.find("meta", attrs={"name": "description"})
            if meta_desc and meta_desc.get("content"):
                description = meta_desc.get("content").strip()
                logger.info(f"Extracted description from meta description")

            # 2. Try Open Graph description
            if not description:
                og_desc = soup.find("meta", attrs={"property": "og:description"})
                if og_desc and og_desc.get("content"):
                    description = og_desc.get("content").strip()
                    logger.info(f"Extracted description from og:description")

            # 3. Try Twitter description
            if not description:
                twitter_desc = soup.find("meta", attrs={"name": "twitter:description"})
                if twitter_desc and twitter_desc.get("content"):
                    description = twitter_desc.get("content").strip()
                    logger.info(f"Extracted description from twitter:description")

            # 4. Try first paragraph
            if not description:
                first_p = soup.find('p')
                if first_p and first_p.get_text():
                    description = first_p.get_text().strip()
                    if len(description) > 150:
                        description = description[:147] + "..."
                    logger.info(f"Extracted description from first paragraph")

            # 5. Generate description from URL if still empty
            if not description and url:
                description = self._generate_description_from_url(url, title)
                logger.info(f"Generated description from URL")

            return title, description

        except Exception as e:
            logger.error(f"Error extracting title and meta: {str(e)}")
            # Even in error case, provide meaningful fallbacks
            if url:
                title = self._generate_title_from_url(url)
                description = self._generate_description_from_url(url, title)
                return title, description
            return "Unknown Page", "Resource information"

    def _generate_title_from_url(self, url: str) -> str:
        """Generate a meaningful title from URL structure.

        Args:
            url: The URL to analyze

        Returns:
            Generated title string
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # Extract path components
            path_parts = [part for part in parsed.path.strip('/').split('/') if part]

            if not path_parts:
                # If no path, use domain name
                domain_parts = parsed.netloc.split('.')
                if len(domain_parts) > 1:
                    return domain_parts[-2].replace('-', ' ').title()
                return parsed.netloc.replace('-', ' ').title()

            # Use the last meaningful path component
            last_part = path_parts[-1]

            # Remove file extensions
            if '.' in last_part:
                last_part = last_part.split('.')[0]

            # Convert to readable title
            title = last_part.replace('-', ' ').replace('_', ' ').title()

            # Add context from parent directories for better titles
            if len(path_parts) > 1:
                parent = path_parts[-2].replace('-', ' ').replace('_', ' ').title()

                # Create contextual titles based on common patterns
                if parent.lower() in ['api', 'docs', 'documentation']:
                    title = f"{title} - {parent}"
                elif title.lower() in ['index', 'home', 'main']:
                    title = f"{parent} Overview"
                elif parent.lower() in ['guide', 'guides', 'tutorial', 'tutorials']:
                    title = f"{title} Guide"
                elif parent.lower() in ['example', 'examples']:
                    title = f"{title} Example"

            return title if title else "Documentation Page"

        except Exception as e:
            logger.error(f"Error generating title from URL {url}: {str(e)}")
            return "Documentation Page"

    def _generate_description_from_url(self, url: str, title: str) -> str:
        """Generate a meaningful description from URL structure and title.

        Args:
            url: The URL to analyze
            title: The extracted or generated title

        Returns:
            Generated description string
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path = parsed.path.lower()

            # Generate contextual descriptions based on URL patterns
            if any(keyword in path for keyword in ['/api/', '/reference/', '/endpoint']):
                return f"API reference documentation for {title.lower()}"
            elif any(keyword in path for keyword in ['/guide/', '/tutorial/', '/how-to']):
                return f"Step-by-step guide covering {title.lower()}"
            elif any(keyword in path for keyword in ['/example', '/demo/', '/sample']):
                return f"Practical example demonstrating {title.lower()}"
            elif any(keyword in path for keyword in ['/getting-started', '/quickstart', '/setup']):
                return f"Getting started guide for {title.lower()}"
            elif any(keyword in path for keyword in ['/dashboard/', '/admin/', '/console']):
                return f"Dashboard documentation for {title.lower()}"
            elif any(keyword in path for keyword in ['/integration/', '/plugin/', '/extension']):
                return f"Integration guide for {title.lower()}"
            elif any(keyword in path for keyword in ['/concept/', '/overview/', '/introduction']):
                return f"Overview and concepts related to {title.lower()}"
            elif any(keyword in path for keyword in ['/faq', '/help', '/support']):
                return f"Help and support information for {title.lower()}"
            else:
                # Generic but meaningful description
                return f"Documentation covering {title.lower()}"

        except Exception as e:
            logger.error(f"Error generating description from URL {url}: {str(e)}")
            return f"Learn about {title.lower()}"

    async def close(self):
        """Close the Puppeteer browser if it's open."""
        if self.browser:
            await self.browser.close()
            self.browser = None


# Synchronous wrapper functions for easier integration
def extract_content_sync(url: str, use_puppeteer: bool = False) -> Tuple[str, str, str]:
    """Synchronous wrapper for content extraction.
    
    Args:
        url: The URL to process
        use_puppeteer: Whether to use Puppeteer for rendering
        
    Returns:
        Tuple of (title, meta_description, main_content)
    """
    extractor = ContentExtractor(use_puppeteer)
    
    try:
        # Run async extraction
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        html_content = loop.run_until_complete(extractor.get_page_content(url))
        
        if html_content:
            title, meta_desc = extractor.extract_title_and_meta(html_content, url)
            main_content = extractor.extract_main_content(html_content)
            
            # Close browser if used
            loop.run_until_complete(extractor.close())
            loop.close()
            
            return title, meta_desc, main_content
        else:
            # No HTML content retrieved, but still provide meaningful fallbacks
            loop.run_until_complete(extractor.close())
            loop.close()
            title = extractor._generate_title_from_url(url)
            description = extractor._generate_description_from_url(url, title)
            return title, description, ""

    except Exception as e:
        logger.error(f"Error in synchronous content extraction for {url}: {str(e)}")
        # Even in error case, provide meaningful fallbacks
        extractor = ContentExtractor(use_puppeteer)
        title = extractor._generate_title_from_url(url)
        description = extractor._generate_description_from_url(url, title)
        return title, description, ""
